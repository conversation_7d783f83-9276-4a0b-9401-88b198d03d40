import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Main } from './features/main/main';
import { Login } from './features/auth/components/login/login';
import { Registration } from './features/auth/components/registration/registration';
import { Profile } from './features/profile/profile';
import { GamesCatalogComponent } from './features/games/games-catalog.component';
import { GameDetailComponent } from './features/games/game-detail.component';
import { CartComponent } from './features/cart/cart.component';
import { GuestCartComponent } from './features/cart/guest-cart.component';
import { AuthGuard } from './core/guards/auth.guard';
import { ProfileSettingsComponent } from './features/profile/components/profile-settings/profile-settings.component';
import { UserLibraryComponent } from './features/profile/components/user-library/user-library.component';

import { ProfileGamesCatalogComponent } from './features/profile/components/profile-games-catalog.component';
import { ProfileGameDetailComponent } from './features/profile/components/profile-game-detail.component';
import { PurchaseHistoryComponent } from './features/profile/components/purchase-history/purchase-history.component';
import { UsersManagementComponent } from './features/profile/components/users-management/users-management.component';
import { UserDetailComponent } from './features/profile/components/user-detail/user-detail.component';
import { GamesManagementComponent } from './features/profile/components/games-management/games-management.component';
import { LibraryManagementComponent } from './features/profile/components/library-management/library-management.component';
import { GameKeysManagementComponent } from './features/profile/components/game-keys-management/game-keys-management.component';
import { GameFilesManagementComponent } from './features/profile/components/game-files-management/game-files-management.component';
import { GameAccessManagementComponent } from './features/profile/components/game-access-management/game-access-management.component';
import { GamePackagesManagementComponent } from './features/profile/components/game-packages-management/game-packages-management.component';
import { MyPackagesComponent } from './features/profile/components/my-packages/my-packages.component';
// Temporarily disabled - packages moved to main page only
// import { ProfilePackagesCatalogComponent } from './features/profile/components/profile-packages-catalog.component';
// import { ProfilePackageDetailComponent } from './features/profile/components/profile-package-detail.component';

const routes: Routes = [
  {
    path: '',
    component: Main,
  },
  {
    path: 'login',
    component: Login
  },
  {
    path: 'registration',
    component: Registration
  },
  {
    path: 'profile',
    component: Profile,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: 'settings',
        pathMatch: 'full'
      },
      {
        path: 'settings',
        component: ProfileSettingsComponent
      },
      {
        path: 'library',
        component: UserLibraryComponent
      },
      {
        path: 'library/games/:id',
        component: ProfileGameDetailComponent
      },
      {
        path: 'my-packages',
        component: MyPackagesComponent
      },

      // Temporarily disabled - games catalog moved to main page
      // {
      //   path: 'games',
      //   component: ProfileGamesCatalogComponent
      // },
      // {
      //   path: 'games/:id',
      //   component: ProfileGameDetailComponent
      // },
      {
        path: 'purchases',
        component: PurchaseHistoryComponent
      },
      {
        path: 'users',
        component: UsersManagementComponent
      },
      {
        path: 'users/:id',
        component: UserDetailComponent
      },
      {
        path: 'admin-games',
        component: GamesManagementComponent
      },
      {
        path: 'admin-library',
        component: LibraryManagementComponent
      },
      {
        path: 'game-keys',
        component: GameKeysManagementComponent
      },
      {
        path: 'game-files',
        component: GameFilesManagementComponent
      },
      {
        path: 'game-access',
        component: GameAccessManagementComponent
      },
      // Temporarily disabled - packages moved to main page only
      // {
      //   path: 'packages',
      //   component: ProfilePackagesCatalogComponent
      // },
      // {
      //   path: 'packages/:id',
      //   component: ProfilePackageDetailComponent
      // },
      {
        path: 'admin-packages',
        component: GamePackagesManagementComponent
      }
    ]
  },
  {
    path: 'games/:id',
    component: GameDetailComponent
  },
  {
    path: 'cart',
    component: CartComponent
  },
  {
    path: '**',
    redirectTo: ''
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
