<!-- Profile Page Container -->
<div
  class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900 pt-20"
>
  <!-- Mobile Admin Sidebar Overlay -->
  <div
    *ngIf="isMobileSidebarOpen && hasAdminAccess()"
    class="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
    (click)="closeMobileSidebar()"
    aria-hidden="true"
  ></div>

  <div class="flex flex-col lg:flex-row min-h-[calc(100vh-5rem)] profile-layout">
    <!-- Admin Sidebar (Only for Staff) -->
    <div
      *ngIf="hasAdminAccess()"
      class="fixed top-20 left-0 w-80 h-[calc(100vh-5rem)] bg-slate-900/95 backdrop-blur-sm border-r border-slate-600/30 flex flex-col profile-sidebar z-50 transform transition-transform duration-300 ease-in-out"
      [class.translate-x-0]="isMobileSidebarOpen"
      [class.-translate-x-full]="!isMobileSidebarOpen"
      [class.lg:translate-x-0]="true"
      role="navigation"
      [attr.aria-hidden]="!isMobileSidebarOpen && 'true'"
      aria-label="Навигация администратора"
    >
      <!-- Mobile Close Button - Top Right Cross -->
      <button
        class="lg:hidden absolute top-3 right-3 text-gray-300 hover:text-white transition-colors p-1.5 hover:bg-slate-700/50 rounded-lg z-10"
        (click)="closeMobileSidebar()"
        aria-label="Закрыть меню"
      >
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>

      <!-- Navigation Menu -->
      <div class="flex-1 p-3 lg:p-4 space-y-4 lg:space-y-6 overflow-y-auto">
        <!-- Administration Section (Only for Staff) -->
        <div class="mb-4 lg:mb-6">
          <h2 class="text-white font-medium text-sm lg:text-base mb-2 lg:mb-3">
            Администрирование
          </h2>
          <div class="space-y-1">
            <a
              routerLink="/profile/users"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Управление пользователями
            </a>
            <a
              routerLink="/profile/admin-games"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Управление играми
            </a>
            <a
              routerLink="/profile/admin-library"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Управление библиотекой
            </a>
            <!-- <a
              routerLink="/profile/game-keys"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Управление ключами игр
            </a> -->
            <a
              routerLink="/profile/game-files"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Управление файлами игр
            </a>
            <a
              routerLink="/profile/game-access"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Управление доступом к играм
            </a>
            <a
              routerLink="/profile/admin-packages"
              routerLinkActive="bg-slate-700/70"
              class="w-full text-left block text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
            >
              Управление пакетами
            </a>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <div class="p-3 lg:p-4 border-t border-slate-600/30">
        <button
          (click)="onLogout()"
          class="w-full bg-slate-700/60 hover:bg-slate-600/70 text-gray-300 hover:text-white font-normal py-2 lg:py-2.5 px-2 lg:px-3 rounded-md border border-slate-600/40 hover:border-slate-500/60 transition-all text-xs lg:text-sm"
        >
          <svg
            class="w-3 h-3 lg:w-4 lg:h-4 inline mr-1 lg:mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            ></path>
          </svg>
          Выйти
        </button>
      </div>
    </div>

    <!-- Mobile Admin Sidebar Open Button (Only for Staff) -->
    <div
      *ngIf="!isMobileSidebarOpen && hasAdminAccess()"
      class="lg:hidden fixed top-20 left-0 z-40"
    >
      <button
        (click)="toggleMobileSidebar()"
        class="bg-slate-900/95 backdrop-blur-sm border-r border-b border-slate-600/30 text-gray-300 hover:text-white transition-colors px-4 py-2 hover:bg-slate-700/50 rounded-br-lg"
        aria-label="Открыть админ меню"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5l7 7-7 7"
          ></path>
        </svg>
      </button>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 profile-content" [class.lg:ml-80]="hasAdminAccess()">
      <div class="px-4 lg:px-8 py-6">
        <div class="max-w-6xl mx-auto">
          <!-- Tab Navigation -->
          <div class="mb-6">
            <!-- Desktop: Horizontal tabs -->
            <div class="hidden sm:flex space-x-1">
              <button
                *ngFor="let tab of tabs"
                (click)="setActiveTab(tab.id)"
                [class]="'flex-1 px-6 py-4 text-sm font-medium transition-all duration-300 whitespace-nowrap text-center relative ' +
                         (activeTab === tab.id ? 'bg-slate-800/50 text-white rounded-t-lg border-b-2 border-blue-400 z-10' :
                          'bg-slate-700/20 text-gray-300 hover:text-white hover:bg-slate-700/40 rounded-t-lg border-b-2 border-transparent')"
              >
                {{ tab.label }}
              </button>
            </div>

            <!-- Mobile: Vertical tabs -->
            <div class="sm:hidden space-y-2">
              <button
                *ngFor="let tab of tabs"
                (click)="setActiveTab(tab.id)"
                [class]="'w-full px-4 py-3 text-xs font-medium transition-all duration-300 text-left relative ' +
                         (activeTab === tab.id ? 'bg-slate-800/50 text-white rounded-lg border-l-4 border-blue-400' :
                          'bg-slate-700/20 text-gray-300 hover:text-white hover:bg-slate-700/40 rounded-lg border-l-4 border-transparent')"
              >
                {{ tab.label }}
              </button>
            </div>

            <!-- Tab content background connector (desktop only) -->
            <div class="hidden sm:block bg-slate-800/50 h-1 -mt-0.5 rounded-t-sm"></div>
          </div>

          <!-- Content Area -->
          <div class="bg-slate-800/50 rounded-lg sm:rounded-b-lg sm:rounded-tr-lg p-4 sm:p-6 lg:p-8">
        <!-- Loading State -->
        <app-loading-spinner *ngIf="isLoading" [overlay]="true">
        </app-loading-spinner>

        <!-- Error State -->
        <div
          *ngIf="errorMessage && !isLoading"
          class="flex items-center justify-center h-64"
        >
          <div class="text-center">
            <div
              class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 max-w-md"
            >
              <h3 class="text-red-300 font-semibold mb-2">Ошибка загрузки</h3>
              <p class="text-red-200 mb-4">{{ errorMessage }}</p>
              <button
                (click)="loadUserProfile()"
                class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Попробовать снова
              </button>
            </div>
          </div>
        </div>

        <!-- Profile Content -->
        <div *ngIf="userProfile && !isLoading">
          <!-- Child Route Content (e.g., game details) -->
          <div *ngIf="isChildRoute">
            <router-outlet></router-outlet>
          </div>

          <!-- Tab Content (when not on child route) -->
          <div *ngIf="!isChildRoute">
            <!-- Settings Tab Content -->
            <app-profile-settings *ngIf="activeTab === 'settings'"></app-profile-settings>

            <!-- Library Tab Content -->
            <app-user-library *ngIf="activeTab === 'library'"></app-user-library>

            <!-- Purchases Tab Content -->
            <app-purchase-history *ngIf="activeTab === 'purchases'"></app-purchase-history>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</div>
